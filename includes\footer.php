    </main>

    <!-- Enhanced Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="footer-brand">
                        <h4 class="mb-3">
                            <i class="fas fa-calendar-star me-2"></i>
                            <span style="font-weight: 800;">Event</span><span style="font-weight: 400;">Booking</span>
                        </h4>
                        <p class="mb-3">Your premier destination for discovering and booking amazing events across Cameroon and Central Africa. Experience the best entertainment, culture, and networking opportunities.</p>
                        <div class="social-links">
                            <a href="#" class="me-3" title="Facebook">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="me-3" title="Twitter">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="me-3" title="Instagram">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="me-3" title="LinkedIn">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <a href="#" title="YouTube">
                                <i class="fab fa-youtube"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-lg-2 col-md-6 mb-4">
                    <h5>Quick Links</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="index.php"><i class="fas fa-home me-2"></i>Home</a></li>
                        <li class="mb-2"><a href="events.php"><i class="fas fa-calendar-alt me-2"></i>Events</a></li>
                        <li class="mb-2"><a href="about.php"><i class="fas fa-heart me-2"></i>About Us</a></li>
                        <li class="mb-2"><a href="contact.php"><i class="fas fa-envelope me-2"></i>Contact</a></li>
                        <?php if (function_exists('isLoggedIn') && isLoggedIn()): ?>
                        <li class="mb-2"><a href="user/dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                        <?php endif; ?>
                    </ul>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <h5>Event Categories</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="events.php?category=concerts"><i class="fas fa-music me-2"></i>Concerts</a></li>
                        <li class="mb-2"><a href="events.php?category=conferences"><i class="fas fa-users me-2"></i>Conferences</a></li>
                        <li class="mb-2"><a href="events.php?category=workshops"><i class="fas fa-tools me-2"></i>Workshops</a></li>
                        <li class="mb-2"><a href="events.php?category=sports"><i class="fas fa-futbol me-2"></i>Sports</a></li>
                        <li class="mb-2"><a href="events.php?category=cultural"><i class="fas fa-theater-masks me-2"></i>Cultural</a></li>
                    </ul>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <h5>Contact Info</h5>
                    <div class="contact-info">
                        <p class="mb-3">
                            <i class="fas fa-envelope me-3"></i>
                            <a href="mailto:<EMAIL>"><EMAIL></a>
                        </p>
                        <p class="mb-3">
                            <i class="fas fa-phone me-3"></i>
                            <a href="tel:+237652731798">+237 652 731 798</a>
                        </p>
                        <p class="mb-3">
                            <i class="fas fa-map-marker-alt me-3"></i>
                            Yaoundé, Cameroon
                        </p>
                        <p class="mb-0">
                            <i class="fas fa-clock me-3"></i>
                            24/7 Customer Support
                        </p>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="mb-0">&copy; 2025 EventBooking Cameroon. All rights reserved.</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <p class="mb-0">
                            <a href="#" class="me-3">Privacy Policy</a>
                            <a href="#" class="me-3">Terms of Service</a>
                            <a href="test-db.php">System Status</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Custom JS -->
    <script src="js/main.js"></script>

    <!-- Enhanced Page Interactions -->
    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add loading states to buttons
        document.querySelectorAll('.btn').forEach(button => {
            button.addEventListener('click', function() {
                if (this.type === 'submit' || this.classList.contains('loading-btn')) {
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';
                    this.disabled = true;

                    // Re-enable after 3 seconds (fallback)
                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.disabled = false;
                    }, 3000);
                }
            });
        });

        // Add fade-in animation to elements as they come into view
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in');
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.querySelectorAll('.event-card, .form-container, .dashboard-card').forEach(el => {
            observer.observe(el);
        });

        // Enhanced navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(255, 255, 255, 0.98)';
                navbar.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.1)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
                navbar.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.05)';
            }
        });

        // Add body padding for fixed navbar
        document.body.style.paddingTop = document.querySelector('.navbar').offsetHeight + 'px';
    </script>

    <?php if (function_exists('isLoggedIn') && isLoggedIn()): ?>
    <!-- Notification System JavaScript -->
    <script>
    // Notification system for logged-in users
    let notificationInterval;

    // Load notifications when page loads
    document.addEventListener('DOMContentLoaded', function() {
        loadNotifications();

        // Check for new notifications every 30 seconds
        notificationInterval = setInterval(loadNotifications, 30000);

        // Load notifications when dropdown is opened
        const notificationDropdown = document.getElementById('notificationDropdown');
        if (notificationDropdown) {
            notificationDropdown.addEventListener('click', function() {
                loadNotifications();
            });
        }
    });

    // Load notifications from server
    function loadNotifications() {
        const currentPath = window.location.pathname;
        const basePath = currentPath.includes('/user/') ? '' : 'user/';

        fetch(basePath + 'check-notifications.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateNotificationBadge(data.unread_count);
                    updateNotificationList(data.notifications);
                }
            })
            .catch(error => {
                console.error('Error loading notifications:', error);
            });
    }

    // Update notification badge
    function updateNotificationBadge(count) {
        const badge = document.getElementById('notificationBadge');
        if (badge) {
            if (count > 0) {
                badge.textContent = count > 99 ? '99+' : count;
                badge.style.display = 'block';

                // Add pulse animation for new notifications
                badge.classList.add('animate-pulse');
                setTimeout(() => badge.classList.remove('animate-pulse'), 2000);
            } else {
                badge.style.display = 'none';
            }
        }
    }

    // Update notification list
    function updateNotificationList(notifications) {
        const list = document.getElementById('notificationList');
        if (!list) return;

        if (notifications.length === 0) {
            list.innerHTML = `
                <div class="dropdown-item text-center text-muted py-4">
                    <i class="fas fa-inbox fa-2x mb-2"></i><br>
                    No notifications yet
                </div>
            `;
            return;
        }

        let html = '';
        notifications.forEach(notification => {
            const readClass = notification.is_read ? '' : 'bg-light border-start border-primary border-3';
            const icon = getNotificationIcon(notification.type);

            html += `
                <div class="dropdown-item notification-item ${readClass}" style="white-space: normal; max-width: 100%;">
                    <div class="d-flex align-items-start">
                        <div class="me-3 mt-1">
                            <i class="${icon} text-primary"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1 fw-bold">${escapeHtml(notification.title)}</h6>
                            <p class="mb-1 text-muted small">${escapeHtml(notification.content)}</p>
                            <small class="text-muted">${notification.time_ago}</small>
                        </div>
                        ${!notification.is_read ? '<div class="ms-2"><span class="badge bg-primary">New</span></div>' : ''}
                    </div>
                </div>
            `;
        });

        list.innerHTML = html;
    }

    // Get icon for notification type
    function getNotificationIcon(type) {
        switch (type) {
            case 'message_reply': return 'fas fa-reply';
            case 'booking': return 'fas fa-ticket-alt';
            case 'system': return 'fas fa-cog';
            default: return 'fas fa-bell';
        }
    }

    // Escape HTML to prevent XSS
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Mark all notifications as read
    function markAllNotificationsRead() {
        const currentPath = window.location.pathname;
        const basePath = currentPath.includes('/user/') ? '' : 'user/';

        fetch(basePath + 'mark-notifications-read.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({action: 'mark_all_read'})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                loadNotifications(); // Reload to update display
                showNotificationToast('All notifications marked as read', 'success');
            }
        })
        .catch(error => {
            console.error('Error marking notifications as read:', error);
        });
    }

    // Show toast notification
    function showNotificationToast(message, type = 'info') {
        // Create toast element
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');
        toast.style.position = 'fixed';
        toast.style.top = '20px';
        toast.style.right = '20px';
        toast.style.zIndex = '9999';

        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;

        document.body.appendChild(toast);

        // Initialize and show toast
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        // Remove from DOM after hiding
        toast.addEventListener('hidden.bs.toast', () => {
            document.body.removeChild(toast);
        });
    }

    // Clean up interval when page unloads
    window.addEventListener('beforeunload', function() {
        if (notificationInterval) {
            clearInterval(notificationInterval);
        }
    });
    </script>

    <!-- Notification Styles -->
    <style>
    .notification-dropdown {
        border: none;
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        border-radius: 10px;
    }

    .notification-item {
        border-radius: 8px;
        margin: 2px 8px;
        transition: all 0.3s ease;
    }

    .notification-item:hover {
        background-color: rgba(0,123,255,0.1) !important;
    }

    .animate-pulse {
        animation: pulse 1s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    #notificationBadge {
        font-size: 0.7rem;
        min-width: 18px;
        height: 18px;
        line-height: 18px;
    }
    </style>
    <?php endif; ?>
</body>
</html>
