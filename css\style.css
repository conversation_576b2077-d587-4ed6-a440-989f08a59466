/* Enhanced Event Booking System - Modern Design */

:root {
    /* Primary Brand Colors - Vibrant Purple & Blue Gradient */
    --primary-color: #667eea;
    --primary-dark: #5a67d8;
    --primary-light: #7c3aed;
    --secondary-color: #764ba2;

    /* Accent Colors - Modern & Vibrant */
    --accent-pink: #f093fb;
    --accent-orange: #f8b500;
    --accent-green: #4ade80;
    --accent-blue: #3b82f6;

    /* Status Colors */
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #06b6d4;

    /* Neutral Colors */
    --light-color: #f8fafc;
    --lighter-color: #ffffff;
    --dark-color: #1e293b;
    --darker-color: #0f172a;
    --gray-color: #64748b;
    --gray-light: #e2e8f0;

    /* Gradient Combinations */
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent-pink) 0%, var(--primary-color) 100%);
    --gradient-success: linear-gradient(135deg, var(--success-color) 0%, var(--accent-green) 100%);
    --gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.1);

    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.7;
    color: var(--dark-color);
    background-color: var(--light-color);
    font-weight: 400;
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    color: var(--dark-color);
    line-height: 1.3;
}

/* Hero Section - Enhanced with Animation */
.hero-section {
    background: var(--gradient-hero);
    color: white;
    padding: 120px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="rgba(255,255,255,0.1)"/><stop offset="100%" stop-color="rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-section h1 {
    font-size: 4rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2;
}

.hero-section p {
    font-size: 1.4rem;
    margin-bottom: 2.5rem;
    opacity: 0.95;
    position: relative;
    z-index: 2;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-section .btn {
    position: relative;
    z-index: 2;
    padding: 15px 40px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
}

.hero-section .btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

/* Enhanced Hero Elements */
.min-vh-75 {
    min-height: 75vh;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-stats {
    margin-top: 3rem;
    opacity: 0.9;
}

.hero-stats .stat-item {
    padding: 1rem;
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    margin: 0 0.5rem;
    transition: all 0.3s ease;
}

.hero-stats .stat-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-5px);
}

.hero-stats h3 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: white;
}

.hero-stats p {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
}

/* Enhanced Features Section */
.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-primary);
    border-radius: 50%;
    color: white;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
}

.feature-icon:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: var(--shadow-xl);
}

.feature-icon i {
    color: white !important;
}

/* Enhanced Statistics Cards */
.stat-card {
    background: var(--gradient-primary);
    color: white;
    border-radius: var(--radius-xl);
    padding: 2.5rem 2rem;
    text-align: center;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.6s ease;
    opacity: 0;
}

.stat-card:hover::before {
    opacity: 1;
    animation: shimmer 1.5s ease-in-out;
}

.stat-card:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: var(--shadow-xl);
}

.stat-card h3 {
    font-size: 3rem;
    font-weight: 900;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 2;
}

.stat-card p {
    font-size: 1.2rem;
    margin: 0;
    font-weight: 600;
    position: relative;
    z-index: 2;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* Enhanced Event Cards */
.event-card {
    border: none;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    margin-bottom: 2rem;
    background: var(--lighter-color);
    position: relative;
}

.event-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.event-card:hover::before {
    transform: scaleX(1);
}

.event-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.event-card img {
    height: 220px;
    object-fit: cover;
    width: 100%;
    transition: transform 0.4s ease;
}

.event-card:hover img {
    transform: scale(1.05);
}

.event-card .card-body {
    padding: 2rem;
    position: relative;
}

.event-card .card-title {
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 0.8rem;
    color: var(--dark-color);
    line-height: 1.3;
}

.event-card .event-date {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 1rem;
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.event-card .event-date i {
    margin-right: 0.5rem;
    color: var(--accent-orange);
}

.event-card .event-location {
    color: var(--gray-color);
    font-size: 1rem;
    margin-bottom: 1.2rem;
    display: flex;
    align-items: center;
}

.event-card .event-location i {
    margin-right: 0.5rem;
    color: var(--accent-pink);
}

.event-card .event-price {
    font-size: 1.8rem;
    font-weight: 800;
    background: var(--gradient-success);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Event Card Badges */
.event-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: var(--gradient-accent);
    color: white;
    padding: 6px 12px;
    border-radius: var(--radius-lg);
    font-size: 0.8rem;
    font-weight: 600;
    box-shadow: var(--shadow-md);
}

.event-availability {
    display: flex;
    align-items: center;
    margin-top: 1rem;
    padding: 0.8rem;
    background: var(--light-color);
    border-radius: var(--radius-md);
}

.availability-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.availability-indicator.available { background: var(--success-color); }
.availability-indicator.limited { background: var(--warning-color); }
.availability-indicator.sold-out { background: var(--danger-color); }

/* Enhanced Search and Filter Section */
.search-section {
    background: linear-gradient(135deg, var(--light-color) 0%, rgba(255, 255, 255, 0.8) 100%);
    padding: 3rem 0;
    border-bottom: 1px solid var(--gray-light);
    position: relative;
}

.search-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(102,126,234,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
}

.search-form {
    background: var(--lighter-color);
    padding: 2.5rem;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(102, 126, 234, 0.1);
    position: relative;
    z-index: 2;
}

.search-form h3 {
    color: var(--dark-color);
    margin-bottom: 1.5rem;
    font-weight: 700;
    text-align: center;
}

.search-form .form-control {
    border-radius: var(--radius-md);
    border: 2px solid var(--gray-light);
    padding: 12px 16px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.search-form .btn {
    border-radius: var(--radius-md);
    padding: 12px 24px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.search-form .btn-primary {
    background: var(--gradient-primary);
    border: none;
    box-shadow: var(--shadow-md);
}

.search-form .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Cart Styles */
.cart-item {
    border: 1px solid #dee2e6;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    background: white;
}

.cart-item img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 8px;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-controls button {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 1px solid #dee2e6;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quantity-controls input {
    width: 60px;
    text-align: center;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 0.25rem;
}

/* Dashboard Styles */
.dashboard-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.dashboard-card .card-header {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border-radius: 15px 15px 0 0 !important;
    padding: 1rem 1.5rem;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    margin-bottom: 2rem;
}

.stat-card h3 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-card p {
    font-size: 1.1rem;
    margin: 0;
}

/* Booking History */
.booking-item {
    border: 1px solid #dee2e6;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    background: white;
    position: relative;
}

.booking-status {
    position: absolute;
    top: 1rem;
    right: 1rem;
}

.booking-status .badge {
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
}

.qr-code {
    text-align: center;
    margin-top: 1rem;
}

.qr-code img {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 0.5rem;
    background: white;
}

/* Enhanced Form Styles */
.form-container {
    background: var(--lighter-color);
    padding: 3rem;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    margin: 3rem 0;
    border: 1px solid rgba(102, 126, 234, 0.1);
    position: relative;
    overflow: hidden;
}

.form-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: var(--gradient-primary);
}

.form-container h2 {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 2rem;
    text-align: center;
    font-weight: 800;
    font-size: 2.2rem;
}

.form-group {
    margin-bottom: 2rem;
    position: relative;
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.8rem;
    display: block;
}

.form-control {
    border-radius: var(--radius-md);
    border: 2px solid var(--gray-light);
    padding: 14px 18px;
    font-size: 1.1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: var(--lighter-color);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
    outline: none;
}

.form-control:hover {
    border-color: var(--primary-light);
}

/* Enhanced Button Styles */
.btn {
    border-radius: var(--radius-md);
    padding: 14px 28px;
    font-weight: 700;
    font-size: 1rem;
    letter-spacing: 0.5px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    border: none;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
    background: var(--gradient-primary);
}

.btn-secondary {
    background: var(--gradient-accent);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: var(--gradient-success);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* Event Details */
.event-details {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin: 2rem 0;
}

.event-details img {
    width: 100%;
    height: 400px;
    object-fit: cover;
}

.event-details .content {
    padding: 2rem;
}

.event-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    margin: 1.5rem 0;
    padding: 1.5rem;
    background: var(--light-color);
    border-radius: 10px;
}

.event-meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.event-meta-item i {
    color: var(--primary-color);
    width: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section h1 {
        font-size: 2.5rem;
    }
    
    .hero-section p {
        font-size: 1rem;
    }
    
    .search-form {
        padding: 1rem;
    }
    
    .event-meta {
        flex-direction: column;
        gap: 1rem;
    }
    
    .cart-item {
        padding: 0.75rem;
    }
    
    .form-container {
        padding: 1rem;
        margin: 1rem 0;
    }
}

/* Loading Spinner */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Enhanced Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: 800;
    font-size: 1.5rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.navbar-nav .nav-link {
    font-weight: 600;
    color: var(--dark-color) !important;
    margin: 0 0.5rem;
    padding: 0.8rem 1.2rem !important;
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background: var(--gradient-primary);
    color: white !important;
    transform: translateY(-2px);
}

.navbar-nav .nav-link.active {
    background: var(--primary-color);
    color: white !important;
}

/* Enhanced Footer */
.footer {
    background: var(--gradient-hero);
    color: white;
    padding: 3rem 0 2rem;
    margin-top: 4rem;
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.footer h5 {
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: white;
}

.footer a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer a:hover {
    color: white;
    transform: translateX(5px);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    margin-top: 2rem;
    padding-top: 2rem;
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
}

/* Enhanced Scrollbar */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: var(--light-color);
    border-radius: var(--radius-md);
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: var(--radius-md);
    border: 2px solid var(--light-color);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gradient-accent);
}

/* Loading States */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--gray-light);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    animation: slideIn 0.3s ease;
}

.notification.success {
    background: var(--gradient-success);
    color: white;
}

.notification.error {
    background: linear-gradient(135deg, var(--danger-color), #f87171);
    color: white;
}

.notification.warning {
    background: linear-gradient(135deg, var(--warning-color), #fbbf24);
    color: white;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
