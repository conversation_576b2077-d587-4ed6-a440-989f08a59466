<?php
// Debug Database Connection Issues
echo "<h1>Database Connection Debug</h1>";

// Test 1: Check if PDO MySQL extension is loaded
echo "<h2>1. PHP Extensions Check</h2>";
if (extension_loaded('pdo_mysql')) {
    echo "✅ PDO MySQL extension is loaded<br>";
} else {
    echo "❌ PDO MySQL extension is NOT loaded<br>";
}

if (extension_loaded('mysqli')) {
    echo "✅ MySQLi extension is loaded<br>";
} else {
    echo "❌ MySQLi extension is NOT loaded<br>";
}

// Test 2: Direct connection to Docker MySQL
echo "<h2>2. Direct Docker MySQL Connection Test</h2>";
try {
    $dsn = "mysql:host=127.0.0.1;port=3307;charset=utf8mb4";
    $pdo = new PDO($dsn, 'root', 'rootpassword');
    echo "✅ Connected to Docker MySQL successfully<br>";
    
    // Test database selection
    $pdo->exec("USE devent_system");
    echo "✅ Selected devent_system database<br>";
    
    // Test a simple query
    $stmt = $pdo->query("SELECT 1 as test");
    $result = $stmt->fetch();
    echo "✅ Query test successful: " . $result['test'] . "<br>";
    
} catch (PDOException $e) {
    echo "❌ Docker MySQL connection failed: " . $e->getMessage() . "<br>";
}

// Test 3: Direct connection to XAMPP MySQL
echo "<h2>3. Direct XAMPP MySQL Connection Test</h2>";
try {
    $dsn = "mysql:host=127.0.0.1;port=3306;charset=utf8mb4";
    $pdo = new PDO($dsn, 'root', '');
    echo "✅ Connected to XAMPP MySQL successfully<br>";
    
    // Check if database exists
    $stmt = $pdo->query("SHOW DATABASES LIKE 'devent_system'");
    $result = $stmt->fetch();
    if ($result) {
        echo "✅ devent_system database exists<br>";
        $pdo->exec("USE devent_system");
        echo "✅ Selected devent_system database<br>";
    } else {
        echo "⚠️ devent_system database does not exist<br>";
        echo "Creating database...<br>";
        $pdo->exec("CREATE DATABASE devent_system");
        echo "✅ Created devent_system database<br>";
    }
    
} catch (PDOException $e) {
    echo "❌ XAMPP MySQL connection failed: " . $e->getMessage() . "<br>";
}

// Test 4: Test our configuration function
echo "<h2>4. Configuration Function Test</h2>";
require_once 'config/database.php';

echo "Environment detection result: " . detectEnvironment() . "<br>";
echo "DB_HOST: " . (defined('DB_HOST') ? DB_HOST : 'Not defined') . "<br>";
echo "DB_PORT: " . (defined('DB_PORT') ? DB_PORT : 'Not defined') . "<br>";
echo "DB_USER: " . (defined('DB_USER') ? DB_USER : 'Not defined') . "<br>";
echo "DB_NAME: " . (defined('DB_NAME') ? DB_NAME : 'Not defined') . "<br>";
echo "DB_ENV: " . (defined('DB_ENV') ? DB_ENV : 'Not defined') . "<br>";

// Test 5: Test our getDBConnection function
echo "<h2>5. getDBConnection() Function Test</h2>";
try {
    $pdo = getDBConnection();
    echo "✅ getDBConnection() successful<br>";
    
    // Test a query
    $stmt = $pdo->query("SELECT 1 as test");
    $result = $stmt->fetch();
    echo "✅ Query through getDBConnection() successful<br>";
    
} catch (Exception $e) {
    echo "❌ getDBConnection() failed: " . $e->getMessage() . "<br>";
}

// Test 6: Check if tables exist
echo "<h2>6. Database Tables Check</h2>";
try {
    $pdo = getDBConnection();
    
    $tables = ['users', 'events', 'bookings'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        $result = $stmt->fetch();
        if ($result) {
            echo "✅ Table '$table' exists<br>";
        } else {
            echo "⚠️ Table '$table' does not exist<br>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Table check failed: " . $e->getMessage() . "<br>";
}

// Test 7: Port connectivity test
echo "<h2>7. Port Connectivity Test</h2>";

// Test Docker port
$dockerConnection = @fsockopen('127.0.0.1', 3307, $errno, $errstr, 5);
if ($dockerConnection) {
    echo "✅ Port 3307 (Docker) is accessible<br>";
    fclose($dockerConnection);
} else {
    echo "❌ Port 3307 (Docker) is not accessible: $errstr ($errno)<br>";
}

// Test XAMPP port
$xamppConnection = @fsockopen('127.0.0.1', 3306, $errno, $errstr, 5);
if ($xamppConnection) {
    echo "✅ Port 3306 (XAMPP) is accessible<br>";
    fclose($xamppConnection);
} else {
    echo "❌ Port 3306 (XAMPP) is not accessible: $errstr ($errno)<br>";
}

echo "<h2>Debug Complete</h2>";
echo "<p><a href='test-db.php'>Back to Test Page</a> | <a href='index.php'>Home</a></p>";
?>
