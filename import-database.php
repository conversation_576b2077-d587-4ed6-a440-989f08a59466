<?php
// Import Database Schema
echo "<h1>Database Schema Import</h1>";

// Read the SQL file
$sqlFile = 'database.sql';
if (!file_exists($sqlFile)) {
    die("Error: database.sql file not found!");
}

$sql = file_get_contents($sqlFile);
if (!$sql) {
    die("Error: Could not read database.sql file!");
}

// Split SQL into individual statements
$statements = array_filter(array_map('trim', explode(';', $sql)));

echo "<h2>Importing to XAMPP MySQL (Port 3306)</h2>";

// Try XAMPP first
try {
    $dsn = "mysql:host=127.0.0.1;port=3306;charset=utf8mb4";
    $pdo = new PDO($dsn, 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✅ Connected to XAMPP MySQL</p>";
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            $successCount++;
            echo "<p style='color: green;'>✅ Executed: " . substr($statement, 0, 50) . "...</p>";
        } catch (PDOException $e) {
            $errorCount++;
            echo "<p style='color: orange;'>⚠️ Warning: " . substr($statement, 0, 50) . "... - " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<p><strong>XAMPP Import Summary: $successCount successful, $errorCount warnings</strong></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ XAMPP MySQL connection failed: " . $e->getMessage() . "</p>";
}

echo "<h2>Importing to Docker MySQL (Port 3307)</h2>";

// Try Docker
try {
    $dsn = "mysql:host=127.0.0.1;port=3307;charset=utf8mb4";
    $pdo = new PDO($dsn, 'root', 'rootpassword');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✅ Connected to Docker MySQL</p>";
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            $successCount++;
            echo "<p style='color: green;'>✅ Executed: " . substr($statement, 0, 50) . "...</p>";
        } catch (PDOException $e) {
            $errorCount++;
            echo "<p style='color: orange;'>⚠️ Warning: " . substr($statement, 0, 50) . "... - " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<p><strong>Docker Import Summary: $successCount successful, $errorCount warnings</strong></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Docker MySQL connection failed: " . $e->getMessage() . "</p>";
}

echo "<h2>Verification</h2>";

// Test our configuration
require_once 'config/database.php';

try {
    $pdo = getDBConnection();
    echo "<p>✅ Application database connection successful!</p>";
    
    // Check tables
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<p><strong>Tables found:</strong></p>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>$table</li>";
    }
    echo "</ul>";
    
    // Check sample data
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM events");
    $result = $stmt->fetch();
    echo "<p>📊 Events in database: " . $result['count'] . "</p>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $result = $stmt->fetch();
    echo "<p>👥 Users in database: " . $result['count'] . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Application connection failed: " . $e->getMessage() . "</p>";
}

echo "<h2>Complete!</h2>";
echo "<p><a href='test-db.php'>Test Database Connection</a> | <a href='index.php'>Go to Application</a></p>";
?>
