<?php
// Smart Database configuration - Auto-detects environment
// Supports both XAMPP local and Docker environments

// Environment detection
function detectEnvironment() {
    // Check if running in Docker container
    if (file_exists('/.dockerenv') || getenv('DOCKER_ENV') === 'true') {
        return 'docker';
    }

    // Check if Docker MySQL is accessible on port 3307
    $dockerConnection = @fsockopen('127.0.0.1', 3307, $errno, $errstr, 1);
    if ($dockerConnection) {
        fclose($dockerConnection);
        return 'docker';
    }

    // Check if XAMPP MySQL is accessible on port 3306
    $xamppConnection = @fsockopen('127.0.0.1', 3306, $errno, $errstr, 1);
    if ($xamppConnection) {
        fclose($xamppConnection);
        return 'xampp';
    }

    // Default to XAMPP if nothing detected
    return 'xampp';
}

// Get current environment
$environment = detectEnvironment();

// Configure database based on environment
if ($environment === 'docker') {
    // Docker MySQL configuration
    define('DB_HOST', '127.0.0.1');
    define('DB_PORT', '3307');
    define('DB_USER', 'root');
    define('DB_PASS', 'rootpassword');
    define('DB_NAME', 'event_booking_system');
    define('DB_ENV', 'Docker MySQL');
} else {
    // XAMPP MySQL configuration
    define('DB_HOST', '127.0.0.1');
    define('DB_PORT', '3306');
    define('DB_USER', 'root');
    define('DB_PASS', ''); // XAMPP default is empty password
    define('DB_NAME', 'event_booking_system');
    define('DB_ENV', 'XAMPP MySQL');
}

// Enhanced database connection with better error handling
function getDBConnection() {
    static $pdo = null;

    // Return existing connection if available
    if ($pdo !== null) {
        return $pdo;
    }

    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ];

    // Try primary environment first
    try {
        // Connect without database first
        $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";charset=utf8mb4";
        $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);

        // Check if database exists, create if not
        $stmt = $pdo->query("SHOW DATABASES LIKE '" . DB_NAME . "'");
        if (!$stmt->fetch()) {
            $pdo->exec("CREATE DATABASE " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            error_log("Created database " . DB_NAME . " in " . DB_ENV);
        }

        // Select the database
        $pdo->exec("USE " . DB_NAME);

        // Log successful connection
        error_log("Database connected successfully using " . DB_ENV . " on " . DB_HOST . ":" . DB_PORT);

        return $pdo;
    } catch(PDOException $e) {
        // Log the error
        error_log("Primary database connection failed: " . $e->getMessage());

        // Try alternative connection
        if (DB_ENV === 'Docker MySQL') {
            // Try XAMPP as fallback
            try {
                $dsn = "mysql:host=127.0.0.1;port=3306;charset=utf8mb4";
                $pdo = new PDO($dsn, 'root', '', $options);

                // Check if database exists, create if not
                $stmt = $pdo->query("SHOW DATABASES LIKE '" . DB_NAME . "'");
                if (!$stmt->fetch()) {
                    $pdo->exec("CREATE DATABASE " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                    error_log("Created database " . DB_NAME . " in XAMPP MySQL (fallback)");
                }

                $pdo->exec("USE " . DB_NAME);
                error_log("Fallback to XAMPP MySQL successful");
                return $pdo;
            } catch(PDOException $fallbackError) {
                error_log("XAMPP fallback failed: " . $fallbackError->getMessage());
            }
        } else {
            // Try Docker as fallback
            try {
                $dsn = "mysql:host=127.0.0.1;port=3307;charset=utf8mb4";
                $pdo = new PDO($dsn, 'root', 'rootpassword', $options);

                // Check if database exists, create if not
                $stmt = $pdo->query("SHOW DATABASES LIKE '" . DB_NAME . "'");
                if (!$stmt->fetch()) {
                    $pdo->exec("CREATE DATABASE " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                    error_log("Created database " . DB_NAME . " in Docker MySQL (fallback)");
                }

                $pdo->exec("USE " . DB_NAME);
                error_log("Fallback to Docker MySQL successful");
                return $pdo;
            } catch(PDOException $fallbackError) {
                error_log("Docker fallback failed: " . $fallbackError->getMessage());
            }
        }

        // If both fail, throw the original error
        throw new Exception("Connection failed to both Docker and XAMPP MySQL. Primary error: " . $e->getMessage());
    }
}

// Enhanced connection testing
function testConnection() {
    try {
        $pdo = getDBConnection();

        // Test with a simple query
        $stmt = $pdo->query("SELECT 1");
        $result = $stmt->fetch();

        return [
            'success' => true,
            'environment' => DB_ENV,
            'host' => DB_HOST,
            'port' => DB_PORT,
            'database' => DB_NAME
        ];
    } catch(Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage(),
            'environment' => DB_ENV ?? 'Unknown'
        ];
    }
}

// Get database environment info
function getDatabaseInfo() {
    return [
        'environment' => DB_ENV,
        'host' => DB_HOST,
        'port' => DB_PORT,
        'database' => DB_NAME,
        'user' => DB_USER
    ];
}
?>
