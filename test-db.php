<?php
// Enhanced Database Connection Test
require_once 'config/database.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Connection Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            margin-bottom: 20px;
        }
        .status-success { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-warning { color: #ffc107; }
        .info-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            margin: 5px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container test-container">
        <div class="test-card">
            <h1 class="text-center mb-4">
                <i class="fas fa-database me-3"></i>
                Database Connection Test
            </h1>

            <?php
            // Get database info
            $dbInfo = getDatabaseInfo();
            echo "<div class='text-center mb-4'>";
            echo "<span class='info-badge'><i class='fas fa-server me-2'></i>" . $dbInfo['environment'] . "</span>";
            echo "<span class='info-badge'><i class='fas fa-network-wired me-2'></i>" . $dbInfo['host'] . ":" . $dbInfo['port'] . "</span>";
            echo "<span class='info-badge'><i class='fas fa-database me-2'></i>" . $dbInfo['database'] . "</span>";
            echo "</div>";

            // Test connection
            $connectionTest = testConnection();

            if ($connectionTest['success']) {
                echo "<div class='alert alert-success text-center'>";
                echo "<h4 class='status-success'><i class='fas fa-check-circle me-2'></i>Connection Successful!</h4>";
                echo "<p class='mb-0'>Connected to <strong>" . $connectionTest['environment'] . "</strong> on " . $connectionTest['host'] . ":" . $connectionTest['port'] . "</p>";
                echo "</div>";

                try {
                    $pdo = getDBConnection();

                    // Test queries
                    echo "<div class='row mt-4'>";

                    // Events count
                    try {
                        $stmt = $pdo->query("SELECT COUNT(*) as count FROM events");
                        $result = $stmt->fetch();
                        echo "<div class='col-md-4 text-center'>";
                        echo "<div class='card border-primary'>";
                        echo "<div class='card-body'>";
                        echo "<i class='fas fa-calendar-alt fa-2x text-primary mb-2'></i>";
                        echo "<h3 class='text-primary'>" . $result['count'] . "</h3>";
                        echo "<p class='card-text'>Events</p>";
                        echo "</div></div></div>";
                    } catch (Exception $e) {
                        echo "<div class='col-md-4 text-center'>";
                        echo "<div class='card border-warning'>";
                        echo "<div class='card-body'>";
                        echo "<i class='fas fa-exclamation-triangle fa-2x text-warning mb-2'></i>";
                        echo "<p class='text-warning'>Events table not found</p>";
                        echo "</div></div></div>";
                    }

                    // Users count
                    try {
                        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
                        $result = $stmt->fetch();
                        echo "<div class='col-md-4 text-center'>";
                        echo "<div class='card border-success'>";
                        echo "<div class='card-body'>";
                        echo "<i class='fas fa-users fa-2x text-success mb-2'></i>";
                        echo "<h3 class='text-success'>" . $result['count'] . "</h3>";
                        echo "<p class='card-text'>Users</p>";
                        echo "</div></div></div>";
                    } catch (Exception $e) {
                        echo "<div class='col-md-4 text-center'>";
                        echo "<div class='card border-warning'>";
                        echo "<div class='card-body'>";
                        echo "<i class='fas fa-exclamation-triangle fa-2x text-warning mb-2'></i>";
                        echo "<p class='text-warning'>Users table not found</p>";
                        echo "</div></div></div>";
                    }

                    // Bookings count
                    try {
                        $stmt = $pdo->query("SELECT COUNT(*) as count FROM bookings");
                        $result = $stmt->fetch();
                        echo "<div class='col-md-4 text-center'>";
                        echo "<div class='card border-info'>";
                        echo "<div class='card-body'>";
                        echo "<i class='fas fa-ticket-alt fa-2x text-info mb-2'></i>";
                        echo "<h3 class='text-info'>" . $result['count'] . "</h3>";
                        echo "<p class='card-text'>Bookings</p>";
                        echo "</div></div></div>";
                    } catch (Exception $e) {
                        echo "<div class='col-md-4 text-center'>";
                        echo "<div class='card border-warning'>";
                        echo "<div class='card-body'>";
                        echo "<i class='fas fa-exclamation-triangle fa-2x text-warning mb-2'></i>";
                        echo "<p class='text-warning'>Bookings table not found</p>";
                        echo "</div></div></div>";
                    }

                    echo "</div>";

                } catch (Exception $e) {
                    echo "<div class='alert alert-warning mt-3'>";
                    echo "<h5><i class='fas fa-exclamation-triangle me-2'></i>Database Connected but Query Failed</h5>";
                    echo "<p class='mb-0'>Error: " . $e->getMessage() . "</p>";
                    echo "</div>";
                }

            } else {
                echo "<div class='alert alert-danger text-center'>";
                echo "<h4 class='status-error'><i class='fas fa-times-circle me-2'></i>Connection Failed!</h4>";
                echo "<p class='mb-0'>Error: " . $connectionTest['error'] . "</p>";
                echo "<p class='mb-0'>Environment: " . $connectionTest['environment'] . "</p>";
                echo "</div>";
            }
            ?>

            <div class="text-center mt-4">
                <a href="index.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-home me-2"></i>Back to Home
                </a>
                <button onclick="location.reload()" class="btn btn-outline-primary btn-lg ms-2">
                    <i class="fas fa-sync-alt me-2"></i>Test Again
                </button>
            </div>
        </div>

        <div class="test-card">
            <h5><i class="fas fa-info-circle me-2"></i>Environment Detection</h5>
            <p class="text-muted mb-0">
                This system automatically detects whether you're using XAMPP (port 3306) or Docker MySQL (port 3307)
                and connects accordingly. If one fails, it automatically tries the other as a fallback.
            </p>
        </div>
    </div>
</body>
</html>
