<?php
// Database Environment Switcher
require_once 'config/database.php';

// Handle switching request
if ($_POST['action'] ?? '' === 'switch') {
    $targetEnv = $_POST['target_env'] ?? '';
    
    if ($targetEnv === 'xampp') {
        // Test XAMPP connection
        try {
            $dsn = "mysql:host=127.0.0.1;port=3306;dbname=devent_system;charset=utf8mb4";
            $pdo = new PDO($dsn, 'root', '');
            $success = true;
            $message = "Successfully switched to XAMPP MySQL!";
        } catch (PDOException $e) {
            $success = false;
            $message = "Failed to connect to XAMPP MySQL: " . $e->getMessage();
        }
    } elseif ($targetEnv === 'docker') {
        // Test Docker connection
        try {
            $dsn = "mysql:host=127.0.0.1;port=3307;dbname=devent_system;charset=utf8mb4";
            $pdo = new PDO($dsn, 'root', 'rootpassword');
            $success = true;
            $message = "Successfully switched to Docker MySQL!";
        } catch (PDOException $e) {
            $success = false;
            $message = "Failed to connect to Docker MySQL: " . $e->getMessage();
        }
    }
}

// Get current database info
$dbInfo = getDatabaseInfo();
$connectionTest = testConnection();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Environment Switcher</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh;">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow-lg border-0" style="border-radius: 20px;">
                    <div class="card-header text-center" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 20px 20px 0 0;">
                        <h2 class="mb-0">
                            <i class="fas fa-database me-3"></i>
                            Database Environment Switcher
                        </h2>
                    </div>
                    <div class="card-body p-4">
                        
                        <?php if (isset($message)): ?>
                        <div class="alert alert-<?php echo $success ? 'success' : 'danger'; ?> alert-dismissible fade show">
                            <i class="fas fa-<?php echo $success ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                            <?php echo $message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Current Status -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h4><i class="fas fa-info-circle me-2 text-primary"></i>Current Status</h4>
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <strong>Environment:</strong> 
                                                <span class="badge bg-<?php echo $connectionTest['success'] ? 'success' : 'danger'; ?>">
                                                    <?php echo $dbInfo['environment']; ?>
                                                </span>
                                            </div>
                                            <div class="col-md-6">
                                                <strong>Connection:</strong> 
                                                <span class="badge bg-<?php echo $connectionTest['success'] ? 'success' : 'danger'; ?>">
                                                    <?php echo $connectionTest['success'] ? 'Connected' : 'Failed'; ?>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-md-6">
                                                <strong>Host:</strong> <?php echo $dbInfo['host']; ?>
                                            </div>
                                            <div class="col-md-6">
                                                <strong>Port:</strong> <?php echo $dbInfo['port']; ?>
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-md-6">
                                                <strong>Database:</strong> <?php echo $dbInfo['database']; ?>
                                            </div>
                                            <div class="col-md-6">
                                                <strong>User:</strong> <?php echo $dbInfo['user']; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Environment Options -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="card h-100 border-primary">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0">
                                            <i class="fas fa-server me-2"></i>XAMPP MySQL
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-unstyled">
                                            <li><strong>Host:</strong> 127.0.0.1</li>
                                            <li><strong>Port:</strong> 3306</li>
                                            <li><strong>User:</strong> root</li>
                                            <li><strong>Password:</strong> (empty)</li>
                                        </ul>
                                        <form method="POST" class="mt-3">
                                            <input type="hidden" name="action" value="switch">
                                            <input type="hidden" name="target_env" value="xampp">
                                            <button type="submit" class="btn btn-primary w-100" 
                                                    <?php echo ($dbInfo['environment'] === 'XAMPP MySQL') ? 'disabled' : ''; ?>>
                                                <i class="fas fa-exchange-alt me-2"></i>
                                                <?php echo ($dbInfo['environment'] === 'XAMPP MySQL') ? 'Currently Active' : 'Switch to XAMPP'; ?>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <div class="card h-100 border-info">
                                    <div class="card-header bg-info text-white">
                                        <h5 class="mb-0">
                                            <i class="fab fa-docker me-2"></i>Docker MySQL
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-unstyled">
                                            <li><strong>Host:</strong> 127.0.0.1</li>
                                            <li><strong>Port:</strong> 3307</li>
                                            <li><strong>User:</strong> root</li>
                                            <li><strong>Password:</strong> rootpassword</li>
                                        </ul>
                                        <form method="POST" class="mt-3">
                                            <input type="hidden" name="action" value="switch">
                                            <input type="hidden" name="target_env" value="docker">
                                            <button type="submit" class="btn btn-info w-100"
                                                    <?php echo ($dbInfo['environment'] === 'Docker MySQL') ? 'disabled' : ''; ?>>
                                                <i class="fas fa-exchange-alt me-2"></i>
                                                <?php echo ($dbInfo['environment'] === 'Docker MySQL') ? 'Currently Active' : 'Switch to Docker'; ?>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Quick Actions -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5><i class="fas fa-tools me-2 text-secondary"></i>Quick Actions</h5>
                                <div class="btn-group w-100" role="group">
                                    <a href="test-db.php" class="btn btn-outline-primary">
                                        <i class="fas fa-vial me-2"></i>Test Connection
                                    </a>
                                    <a href="index.php" class="btn btn-outline-success">
                                        <i class="fas fa-home me-2"></i>Back to Home
                                    </a>
                                    <a href="http://localhost:8081" target="_blank" class="btn btn-outline-info">
                                        <i class="fas fa-database me-2"></i>phpMyAdmin
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Instructions -->
                        <div class="alert alert-info mt-4">
                            <h6><i class="fas fa-lightbulb me-2"></i>How it works:</h6>
                            <p class="mb-0">
                                The system automatically detects which database is available and connects accordingly. 
                                You can manually test connections using the buttons above. Make sure your chosen 
                                database server is running before switching.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
